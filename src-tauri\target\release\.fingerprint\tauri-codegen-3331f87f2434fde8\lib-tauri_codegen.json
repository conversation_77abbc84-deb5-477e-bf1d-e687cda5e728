{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 8879837628796626640, "deps": [[654232091421095663, "tauri_utils", false, 11784886597986739627], [3060637413840920116, "proc_macro2", false, 5833869361467354012], [3150220818285335163, "url", false, 15476090353056047903], [4899080583175475170, "semver", false, 13088981244209601912], [4974441333307933176, "syn", false, 11389782038689386283], [7170110829644101142, "json_patch", false, 13558801792294983731], [7392050791754369441, "ico", false, 10215503634606366760], [8319709847752024821, "uuid", false, 845089626108832176], [8569119365930580996, "serde_json", false, 18014180362223342170], [9556762810601084293, "brotli", false, 17432405951254446789], [9689903380558560274, "serde", false, 7376454912803426810], [9857275760291862238, "sha2", false, 8807553530799729956], [10806645703491011684, "thiserror", false, 7325299296600646083], [12687914511023397207, "png", false, 11332526338420078312], [13077212702700853852, "base64", false, 6161113126714645131], [15622660310229662834, "walkdir", false, 7250979821894126862], [17990358020177143287, "quote", false, 7063110427199514833]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-3331f87f2434fde8\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}